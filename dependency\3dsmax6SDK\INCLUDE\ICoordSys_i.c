/* this file contains the actual definitions of */
/* the IIDs and CLSIDs */

/* link this file in with the server and any clients */


/* File created by MIDL compiler version 5.01.0164 */
/* at Sun Jul 25 11:57:48 1999
 */
/* Compiler settings for ICoordSys.idl:
    Oicf (OptLev=i2), W1, Zp8, env=Win32, ms_ext, c_ext
    error checks: allocation ref bounds_check enum stub_data 
*/
//@@MIDL_FILE_HEADING(  )
#ifdef __cplusplus
extern "C"{
#endif 


#ifndef __IID_DEFINED__
#define __IID_DEFINED__

typedef struct _IID
{
    unsigned long x;
    unsigned short s1;
    unsigned short s2;
    unsigned char  c[8];
} IID;

#endif // __IID_DEFINED__

#ifndef CLSID_DEFINED
#define CLSID_DEFINED
typedef IID CLSID;
#endif // CLSID_DEFINED

const IID IID_IGcsBitmap = {0x189618F0,0xF8FD,0x11d2,{0xAA,0x0F,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID IID_IGcsPointTab = {0x90F5DD70,0xE2F6,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID IID_IGcsNodeTab = {0x90F5DD71,0xE2F6,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID IID_IGcsTriObject = {0x90F5DD72,0xE2F6,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID IID_IGcsSplineShape = {0x90F5DD73,0xE2F6,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID IID_IGcsTransform = {0x90F5DD74,0xE2F6,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID IID_IGcsUI = {0x90F5DD76,0xE2F6,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID IID_IGcsLoad = {0x90F5DD77,0xE2F6,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID IID_IGcsSave = {0x90F5DD78,0xE2F6,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID IID_IGcsSession = {0x90F5DD79,0xE2F6,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const IID LIBID_GCSAPILib = {0xCEFFE1D4,0xE309,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const CLSID CLSID_GcsSession = {0xCEFFE1D5,0xE309,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const CLSID CLSID_GcsPointTab = {0xCEFFE1D8,0xE309,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const CLSID CLSID_GcsNodeTab = {0xCEFFE1D9,0xE309,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const CLSID CLSID_GcsTriObject = {0xCEFFE1DA,0xE309,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const CLSID CLSID_GcsSplineShape = {0xCEFFE1DB,0xE309,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const CLSID CLSID_GcsLoad = {0xCEFFE1DC,0xE309,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const CLSID CLSID_GcsSave = {0xCEFFE1DD,0xE309,0x11d2,{0xA9,0xFE,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


const CLSID CLSID_GcsBitmap = {0x189618F1,0xF8FD,0x11d2,{0xAA,0x0F,0x00,0x60,0xB0,0x57,0xDA,0xFB}};


#ifdef __cplusplus
}
#endif

